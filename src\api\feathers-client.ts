import { createClient } from 'ap-api-feathers';
import axios from 'axios';

import authenticationClient from '@feathersjs/authentication-client';
import rest from '@feathersjs/rest-client';

const axiosInstance = axios.create({
  headers: {
    'Content-Type': 'application/json'
  }
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

const restClient = rest(import.meta.env.VITE_API_URL || 'http://localhost:3030');

export const app = createClient(restClient.axios(axiosInstance));

app.configure(authenticationClient({
  storage: window.localStorage,
  storageKey: 'feathers-jwt',

}));

export const api = {
  clubs: app.service('clubs'),
  equipment: app.service('equipment'),
  matchResults: app.service('match-results'),
  matchRegistrations: app.service('match-registrations'),
  matches: app.service('matches'),
  messages: app.service('messages'),
  organizers: app.service('organizers'),
  players: app.service('players'),
  tournaments: app.service('tournaments'),
  users: app.service('users'),
  userMe: app.service('users/me')
};

export type {
  Club, ClubData, ClubQuery, ClubPatch,
  Equipment, EquipmentData, EquipmentQuery, EquipmentPatch,
  MatchResult, MatchResultData, MatchResultQuery, MatchResultPatch,
  MatchRegistration, MatchRegistrationData, MatchRegistrationQuery, MatchRegistrationPatch,
  Match, MatchData, MatchQuery, MatchPatch,
  Message, MessageData, MessageQuery, MessagePatch,
  Organizer, OrganizerData, OrganizerQuery, OrganizerPatch,
  Player, PlayerData, PlayerQuery, PlayerPatch,
  User, UserData, UserQuery, UserPatch,
  Tournament, TournamentData, TournamentQuery, TournamentPatch,
  Federation, FederationData, FederationQuery, FederationPatch,
} from 'ap-api-feathers';
